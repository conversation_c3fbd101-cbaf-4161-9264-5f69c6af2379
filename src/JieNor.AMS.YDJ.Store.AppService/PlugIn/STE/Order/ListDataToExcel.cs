using System;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("listdatatoexcel")]
    public class ListDataToExcel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义事件中，提供额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case "afterListData":
                    var data = e.EventData as QueryDataInfo;
                    UpDataInfo(data);
                    break;
            }
        }

        /// <summary>
        /// 导出的时候更新
        /// </summary>
        /// <param name="datas"></param>
        private void UpDataInfo(QueryDataInfo datas)
        {
            HtmlForm lookupForm = this.OperationContext?.HtmlForm;
            if (datas == null || (datas != null && datas.OfficeDatas == null)) return;
            if (!datas.OfficeDatas.ContainsKey("fstoreid")) return;

            // 获取数据状态和销售部门数据
            var fstatusData = datas.OfficeDatas.ContainsKey("fstatus") ? datas.OfficeDatas["fstatus"] : null;
            var fdeptidData = datas.OfficeDatas.ContainsKey("fdeptid") ? datas.OfficeDatas["fdeptid"] : null;
            var fstoreidData = datas.OfficeDatas["fstoreid"];

            // 如果缺少必要字段，尝试从数据库获取
            if (fstatusData == null || fdeptidData == null)
            {
                var missingFieldsData = GetMissingFieldsData(datas, fstatusData == null, fdeptidData == null);
                if (fstatusData == null) fstatusData = missingFieldsData.StatusData;
                if (fdeptidData == null) fdeptidData = missingFieldsData.DeptIdData;
            }

            // 如果仍然缺少关键字段，记录日志并返回
            if (fdeptidData == null || fstoreidData == null)
            {
                this.Context.Logger?.Warn($"导出数据更新失败：缺少必要字段 - fdeptid: {fdeptidData == null}, fstoreid: {fstoreidData == null}");
                return;
            }

            // 确保数据行数一致
            var rowCount = fstoreidData.Count;
            if (fstatusData != null && fstatusData.Count != rowCount)
            {
                this.Context.Logger?.Warn($"数据行数不一致：fstatus({fstatusData.Count}) vs fstoreid({rowCount})");
                fstatusData = null; // 如果行数不一致，忽略状态字段
            }
            if (fdeptidData.Count != rowCount)
            {
                this.Context.Logger?.Warn($"数据行数不一致：fdeptid({fdeptidData.Count}) vs fstoreid({rowCount})");
                return;
            }

            // 收集所有需要查询的部门ID
            var deptIdsToQuery = new HashSet<string>();
            for (int i = 0; i < rowCount; i++)
            {
                var status = fstatusData != null ? Convert.ToString(fstatusData[i]) : "";
                var deptId = Convert.ToString(fdeptidData[i]);

                // 当数据状态不为已提交(D)或已审核(E)时，收集部门ID
                // 如果没有状态字段，则处理所有记录
                if ((fstatusData == null || (!status.Equals("D") && !status.Equals("E"))) &&
                    !deptId.IsNullOrEmptyOrWhiteSpace())
                {
                    deptIdsToQuery.Add(deptId);
                }
            }

            // 如果没有需要查询的部门ID，直接返回
            if (!deptIdsToQuery.Any()) return;

            // 一次性查询所有部门对应的门店编码
            var deptStoreMapping = GetStoreCodesByDeptIds(deptIdsToQuery.ToList());

            // 更新数据
            for (int i = 0; i < rowCount; i++)
            {
                var status = fstatusData != null ? Convert.ToString(fstatusData[i]) : "";
                var deptId = Convert.ToString(fdeptidData[i]);

                // 当数据状态不为已提交(D)或已审核(E)时，更新fstoreid
                // 如果没有状态字段，则处理所有记录
                if ((fstatusData == null || (!status.Equals("D") && !status.Equals("E"))) &&
                    !deptId.IsNullOrEmptyOrWhiteSpace())
                {
                    if (deptStoreMapping.ContainsKey(deptId) && !deptStoreMapping[deptId].IsNullOrEmptyOrWhiteSpace())
                    {
                        fstoreidData[i] = deptStoreMapping[deptId];
                    }
                }
            }
        }

        /// <summary>
        /// 获取缺失字段的数据
        /// </summary>
        /// <param name="datas">查询数据信息</param>
        /// <param name="needStatus">是否需要获取状态字段</param>
        /// <param name="needDeptId">是否需要获取部门ID字段</param>
        /// <returns>缺失字段的数据</returns>
        private (List<object> StatusData, List<object> DeptIdData) GetMissingFieldsData(QueryDataInfo datas, bool needStatus, bool needDeptId)
        {
            List<object> statusData = null;
            List<object> deptIdData = null;

            if (!needStatus && !needDeptId) return (statusData, deptIdData);

            try
            {
                // 获取主键字段数据，用于查询缺失字段
                var primaryKeyData = GetPrimaryKeyData(datas);
                if (primaryKeyData == null || !primaryKeyData.Any()) return (statusData, deptIdData);

                // 构建查询SQL
                var selectFields = new List<string>();
                if (needStatus) selectFields.Add("fstatus");
                if (needDeptId) selectFields.Add("fdeptid");

                var inClause = string.Join(",", primaryKeyData.Select((id, index) => $"@id{index}"));
                var sql = $@"SELECT fid, {string.Join(", ", selectFields)}
                            FROM ydj_order
                            WHERE fid IN ({inClause}) AND fmainorgid = @fmainorgid
                            ORDER BY fid";

                var sqlParams = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
                };

                // 添加主键参数
                for (int i = 0; i < primaryKeyData.Count; i++)
                {
                    sqlParams.Add(new SqlParam($"@id{i}", DbType.String, primaryKeyData[i]));
                }

                var queryResult = this.DBService.ExecuteDynamicObject(this.Context, sql, sqlParams);

                if (queryResult != null && queryResult.Any())
                {
                    // 创建ID到数据的映射
                    var dataMapping = queryResult.ToDictionary(r => Convert.ToString(r["fid"]));

                    // 按原始数据顺序构建结果
                    if (needStatus)
                    {
                        statusData = new List<object>();
                        foreach (var id in primaryKeyData)
                        {
                            statusData.Add(dataMapping.ContainsKey(id) ? dataMapping[id]["fstatus"] : "");
                        }
                    }

                    if (needDeptId)
                    {
                        deptIdData = new List<object>();
                        foreach (var id in primaryKeyData)
                        {
                            deptIdData.Add(dataMapping.ContainsKey(id) ? dataMapping[id]["fdeptid"] : "");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                this.Context.Logger?.Error($"获取缺失字段数据失败，错误：{ex.Message}");
            }

            return (statusData, deptIdData);
        }

        /// <summary>
        /// 获取主键字段数据
        /// </summary>
        /// <param name="datas">查询数据信息</param>
        /// <returns>主键数据列表</returns>
        private List<string> GetPrimaryKeyData(QueryDataInfo datas)
        {
            // 尝试常见的主键字段名
            var possibleKeyFields = new[] { "fid", "id", "FID", "ID" };

            foreach (var keyField in possibleKeyFields)
            {
                if (datas.OfficeDatas.ContainsKey(keyField))
                {
                    return datas.OfficeDatas[keyField].Select(x => Convert.ToString(x)).ToList();
                }
            }

            return null;
        }

        /// <summary>
        /// 批量根据销售部门ID获取关联门店编码
        /// </summary>
        /// <param name="deptIds">销售部门ID列表</param>
        /// <returns>部门ID与门店编码的映射字典</returns>
        private Dictionary<string, string> GetStoreCodesByDeptIds(List<string> deptIds)
        {
            var result = new Dictionary<string, string>();

            if (deptIds == null || !deptIds.Any()) return result;

            // 构建IN条件的参数
            var inClause = string.Join(",", deptIds.Select((id, index) => $"@deptId{index}"));

            var sql = $@"SELECT dept.fid as deptid, store.fnumber
                            FROM t_bd_department dept with(nolock)
                            INNER JOIN t_bas_store store with(nolock) ON store.fid = dept.fstore
                            WHERE dept.fid IN ({inClause}) 
                            ORDER BY dept.fid, store.fnumber";

            var sqlParams = new List<SqlParam>();

            // 添加部门ID参数
            for (int i = 0; i < deptIds.Count; i++)
            {
                sqlParams.Add(new SqlParam($"@deptId{i}", DbType.String, deptIds[i]));
            }

            var queryResult = this.DBService.ExecuteDynamicObject(this.Context, sql, sqlParams);

            if (queryResult != null && queryResult.Any())
            {
                // 按部门ID分组，合并同一部门的多个门店编码
                var groupedResult = queryResult.GroupBy(r => Convert.ToString(r["deptid"]))
                    .ToDictionary(
                        g => g.Key,
                        g => string.Join(",", g.Select(r => Convert.ToString(r["fnumber"]))
                            .Where(code => !code.IsNullOrEmptyOrWhiteSpace())
                            .Distinct()
                            .OrderBy(code => code))
                    );

                return groupedResult;
            }

            return result;
        }
    }
}