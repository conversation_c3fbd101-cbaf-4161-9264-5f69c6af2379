using System;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.STE.Order
{
    /// <summary>
    /// 订单数据模型
    /// </summary>
    public class OrderDataItem
    {
        public string Id { get; set; }
        public string Status { get; set; }
        public string DeptId { get; set; }
        public string StoreId { get; set; }
    }

    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("listdatatoexcel")]
    public class ListDataToExcel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义事件中，提供额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case "afterListData":
                    var data = e.EventData as QueryDataInfo;
                    UpDataInfo(data);
                    break;
            }
        }

        /// <summary>
        /// 导出的时候更新（使用改进的数据结构）
        /// </summary>
        /// <param name="datas"></param>
        private void UpDataInfo(QueryDataInfo datas)
        {
            HtmlForm lookupForm = this.OperationContext?.HtmlForm;
            if (datas == null || (datas != null && datas.OfficeDatas == null)) return;
            if (!datas.OfficeDatas.ContainsKey("fstoreid")) return;

            // 构建完整的订单数据列表
            var orderDataList = BuildOrderDataList(datas);
            if (orderDataList == null || !orderDataList.Any()) return;

            // 收集所有需要查询的部门ID
            var deptIdsToQuery = new HashSet<string>();
            foreach (var orderData in orderDataList)
            {
                // 当数据状态不为已提交(D)或已审核(E)时，收集部门ID
                // 如果没有状态字段，则处理所有记录
                if ((string.IsNullOrEmpty(orderData.Status) || (!orderData.Status.Equals("D") && !orderData.Status.Equals("E"))) &&
                    !orderData.DeptId.IsNullOrEmptyOrWhiteSpace())
                {
                    deptIdsToQuery.Add(orderData.DeptId);
                }
            }

            // 如果没有需要查询的部门ID，直接返回
            if (!deptIdsToQuery.Any()) return;

            // 一次性查询所有部门对应的门店编码
            var deptStoreMapping = GetStoreCodesByDeptIds(deptIdsToQuery.ToList());

            // 更新数据并写回到原始数据结构
            var fstoreidData = datas.OfficeDatas["fstoreid"];
            for (int i = 0; i < orderDataList.Count; i++)
            {
                var orderData = orderDataList[i];

                // 当数据状态不为已提交(D)或已审核(E)时，更新fstoreid
                // 如果没有状态字段，则处理所有记录
                if ((string.IsNullOrEmpty(orderData.Status) || (!orderData.Status.Equals("D") && !orderData.Status.Equals("E"))) &&
                    !orderData.DeptId.IsNullOrEmptyOrWhiteSpace())
                {
                    if (deptStoreMapping.ContainsKey(orderData.DeptId) && !deptStoreMapping[orderData.DeptId].IsNullOrEmptyOrWhiteSpace())
                    {
                        fstoreidData[i] = deptStoreMapping[orderData.DeptId];
                    }
                }
            }
        }

        /// <summary>
        /// 构建完整的订单数据列表（使用您建议的数据结构）
        /// </summary>
        /// <param name="datas">查询数据信息</param>
        /// <returns>订单数据列表</returns>
        private List<OrderDataItem> BuildOrderDataList(QueryDataInfo datas)
        {
            // 获取主键数据
                var primaryKeyData = GetPrimaryKeyData(datas);
                if (primaryKeyData == null || !primaryKeyData.Any())
                {
                    return null;
                }

                var rowCount = primaryKeyData.Count;
                var orderDataList = new List<OrderDataItem>();

                // 获取现有字段数据
                var fstatusData = datas.OfficeDatas.ContainsKey("fstatus") ? datas.OfficeDatas["fstatus"] : null;
                var fdeptidData = datas.OfficeDatas.ContainsKey("fdeptid") ? datas.OfficeDatas["fdeptid"] : null;
                var fstoreidData = datas.OfficeDatas["fstoreid"];

                // 如果缺少必要字段，从数据库获取
                Dictionary<string, OrderDataItem> missingDataMapping = null;
                if (fstatusData == null || fdeptidData == null)
                {
                    missingDataMapping = GetMissingFieldsFromDatabase(primaryKeyData, fstatusData == null, fdeptidData == null);
                }

                // 构建订单数据列表
                for (int i = 0; i < rowCount; i++)
                {
                    var id = primaryKeyData[i];
                    var orderData = new OrderDataItem
                    {
                        Id = id,
                        StoreId = Convert.ToString(fstoreidData[i])
                    };

                    // 设置状态字段
                    if (fstatusData != null && i < fstatusData.Count)
                    {
                        orderData.Status = Convert.ToString(fstatusData[i]);
                    }
                    else if (missingDataMapping != null && missingDataMapping.ContainsKey(id))
                    {
                        orderData.Status = missingDataMapping[id].Status;
                    }

                    // 设置部门ID字段
                    if (fdeptidData != null && i < fdeptidData.Count)
                    {
                        orderData.DeptId = Convert.ToString(fdeptidData[i]);
                    }
                    else if (missingDataMapping != null && missingDataMapping.ContainsKey(id))
                    {
                        orderData.DeptId = missingDataMapping[id].DeptId;
                    }

                    orderDataList.Add(orderData);
                }
                return orderDataList;
        }

        /// <summary>
        /// 从数据库获取缺失字段数据（返回字典映射）
        /// </summary>
        /// <param name="primaryKeys">主键列表</param>
        /// <param name="needStatus">是否需要获取状态字段</param>
        /// <param name="needDeptId">是否需要获取部门ID字段</param>
        /// <returns>主键到订单数据的映射</returns>
        private Dictionary<string, OrderDataItem> GetMissingFieldsFromDatabase(List<string> primaryKeys, bool needStatus, bool needDeptId)
        {
            var result = new Dictionary<string, OrderDataItem>();

            if (!needStatus && !needDeptId) return result;

            // 构建查询SQL
            var selectFields = new List<string> { "fid" };
            if (needStatus) selectFields.Add("fstatus");
            if (needDeptId) selectFields.Add("fdeptid");

            var inClause = string.Join(",", primaryKeys.Select((id, index) => $"@id{index}"));
            var sql = $@"SELECT {string.Join(", ", selectFields)}
                            FROM t_ydj_order with(nolock)
                            WHERE fid IN ({inClause}) AND fmainorgid = @fmainorgid";

            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
            };

            // 添加主键参数
            for (int i = 0; i < primaryKeys.Count; i++)
            {
                sqlParams.Add(new SqlParam($"@id{i}", DbType.String, primaryKeys[i]));
            }

            var queryResult = this.DBService.ExecuteDynamicObject(this.Context, sql, sqlParams);

            if (queryResult != null && queryResult.Any())
            {
                foreach (var row in queryResult)
                {
                    var id = Convert.ToString(row["fid"]);
                    if (!string.IsNullOrEmpty(id))
                    {
                        result[id] = new OrderDataItem
                        {
                            Id = id,
                            Status = needStatus ? Convert.ToString(row["fstatus"] ?? "") : "",
                            DeptId = needDeptId ? Convert.ToString(row["fdeptid"] ?? "") : ""
                        };
                    }
                }
            }

            return result;
        }
        
        /// <summary>
        /// 获取主键字段数据
        /// </summary>
        /// <param name="datas">查询数据信息</param>
        /// <returns>主键数据列表</returns>
        private List<string> GetPrimaryKeyData(QueryDataInfo datas)
        {
            // 尝试常见的主键字段名
            var possibleKeyFields = new[] { "fid", "id", "FID", "ID" };

            foreach (var keyField in possibleKeyFields)
            {
                if (datas.OfficeDatas.ContainsKey(keyField))
                {
                    var primaryKeys = datas.OfficeDatas[keyField].Select(x => Convert.ToString(x)).ToList();
                    return primaryKeys;
                }
            }

            // 如果没有找到标准主键字段，记录可用字段
            var availableFields = string.Join(", ", datas.OfficeDatas.Keys);
            return null;
        }

        /// <summary>
        /// 批量根据销售部门ID获取关联门店编码
        /// </summary>
        /// <param name="deptIds">销售部门ID列表</param>
        /// <returns>部门ID与门店编码的映射字典</returns>
        private Dictionary<string, string> GetStoreCodesByDeptIds(List<string> deptIds)
        {
            var result = new Dictionary<string, string>();

            if (deptIds == null || !deptIds.Any()) return result;

            // 构建IN条件的参数
            var inClause = string.Join(",", deptIds.Select((id, index) => $"@deptId{index}"));

            var sql = $@"SELECT dept.fid as deptid, store.fnumber
                            FROM t_bd_department dept with(nolock)
                            INNER JOIN t_bas_store store with(nolock) ON store.fid = dept.fstore
                            WHERE dept.fid IN ({inClause}) 
                            ORDER BY dept.fid, store.fnumber";

            var sqlParams = new List<SqlParam>();

            // 添加部门ID参数
            for (int i = 0; i < deptIds.Count; i++)
            {
                sqlParams.Add(new SqlParam($"@deptId{i}", DbType.String, deptIds[i]));
            }

            var queryResult = this.DBService.ExecuteDynamicObject(this.Context, sql, sqlParams);

            if (queryResult != null && queryResult.Any())
            {
                // 按部门ID分组，合并同一部门的多个门店编码
                var groupedResult = queryResult.GroupBy(r => Convert.ToString(r["deptid"]))
                    .ToDictionary(
                        g => g.Key,
                        g => string.Join(",", g.Select(r => Convert.ToString(r["fnumber"]))
                            .Where(code => !code.IsNullOrEmptyOrWhiteSpace())
                            .Distinct()
                            .OrderBy(code => code))
                    );

                return groupedResult;
            }

            return result;
        }
    }
}