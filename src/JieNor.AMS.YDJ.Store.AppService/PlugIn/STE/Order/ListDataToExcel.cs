using System;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("listdatatoexcel")]
    public class ListDataToExcel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义事件中，提供额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case "afterListData":
                    var data = e.EventData as QueryDataInfo;
                    UpDataInfo(data);
                    break;
            }
        }

        /// <summary>
        /// 导出的时候更新
        /// </summary>
        /// <param name="datas"></param>
        private void UpDataInfo(QueryDataInfo datas)
        {
            HtmlForm lookupForm = this.OperationContext?.HtmlForm;
            if (datas == null || (datas != null && datas.OfficeDatas == null)) return;
            if (!datas.OfficeDatas.ContainsKey("fstoreid")) return;

            // 获取数据状态和销售部门数据
            var fstatusData = datas.OfficeDatas.ContainsKey("fstatus") ? datas.OfficeDatas["fstatus"] : null;
            var fdeptidData = datas.OfficeDatas.ContainsKey("fdeptid") ? datas.OfficeDatas["fdeptid"] : null;
            var fstoreidData = datas.OfficeDatas["fstoreid"];

            if (fstatusData == null || fdeptidData == null || fstoreidData == null) return;

            // 收集所有需要查询的部门ID
            var deptIdsToQuery = new HashSet<string>();
            for (int i = 0; i < fstatusData.Count; i++)
            {
                var status = Convert.ToString(fstatusData[i]);
                var deptId = Convert.ToString(fdeptidData[i]);

                // 当数据状态不为已提交(D)或已审核(E)时，收集部门ID
                if (!status.Equals("D") && !status.Equals("E") && !deptId.IsNullOrEmptyOrWhiteSpace())
                {
                    deptIdsToQuery.Add(deptId);
                }
            }

            // 如果没有需要查询的部门ID，直接返回
            if (!deptIdsToQuery.Any()) return;

            // 一次性查询所有部门对应的门店编码
            var deptStoreMapping = GetStoreCodesByDeptIds(deptIdsToQuery.ToList());

            // 更新数据
            for (int i = 0; i < fstatusData.Count; i++)
            {
                var status = Convert.ToString(fstatusData[i]);
                var deptId = Convert.ToString(fdeptidData[i]);

                // 当数据状态不为已提交(D)或已审核(E)时，更新fstoreid
                if (!status.Equals("D") && !status.Equals("E") && !deptId.IsNullOrEmptyOrWhiteSpace())
                {
                    if (deptStoreMapping.ContainsKey(deptId) && !deptStoreMapping[deptId].IsNullOrEmptyOrWhiteSpace())
                    {
                        fstoreidData[i] = deptStoreMapping[deptId];
                    }
                }
            }
        }

        /// <summary>
        /// 批量根据销售部门ID获取关联门店编码
        /// </summary>
        /// <param name="deptIds">销售部门ID列表</param>
        /// <returns>部门ID与门店编码的映射字典</returns>
        private Dictionary<string, string> GetStoreCodesByDeptIds(List<string> deptIds)
        {
            var result = new Dictionary<string, string>();

            if (deptIds == null || !deptIds.Any()) return result;

            // 构建IN条件的参数
            var inClause = string.Join(",", deptIds.Select((id, index) => $"@deptId{index}"));

            var sql = $@"SELECT dept.fid as deptid, store.fnumber
                            FROM t_bd_department dept with(nolock)
                            INNER JOIN t_bas_store store with(nolock) ON store.fid = dept.fstore
                            WHERE dept.fid IN ({inClause}) 
                            ORDER BY dept.fid, store.fnumber";

            var sqlParams = new List<SqlParam>();

            // 添加部门ID参数
            for (int i = 0; i < deptIds.Count; i++)
            {
                sqlParams.Add(new SqlParam($"@deptId{i}", DbType.String, deptIds[i]));
            }

            var queryResult = this.DBService.ExecuteDynamicObject(this.Context, sql, sqlParams);

            if (queryResult != null && queryResult.Any())
            {
                // 按部门ID分组，合并同一部门的多个门店编码
                var groupedResult = queryResult.GroupBy(r => Convert.ToString(r["deptid"]))
                    .ToDictionary(
                        g => g.Key,
                        g => string.Join(",", g.Select(r => Convert.ToString(r["fnumber"]))
                            .Where(code => !code.IsNullOrEmptyOrWhiteSpace())
                            .Distinct()
                            .OrderBy(code => code))
                    );

                return groupedResult;
            }

            return result;
        }
    }
}