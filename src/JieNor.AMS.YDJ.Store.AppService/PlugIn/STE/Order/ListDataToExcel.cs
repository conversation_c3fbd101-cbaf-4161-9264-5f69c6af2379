using System;
using System.Linq;
using System.Collections.Generic;
using System.Data;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.STE.Order
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("listdatatoexcel")]
    public class ListDataToExcel : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 自定义事件中，提供额外过滤条件
        /// </summary>
        /// <param name="e"></param>
        public override void OnCustomServiceEvent(OnCustomServiceEventArgs e)
        {
            switch (e.EventName)
            {
                case "afterListData":
                    var data = e.EventData as QueryDataInfo;
                    UpDataInfo(data);
                    break;
            }
        }

        /// <summary>
        /// 导出的时候更新
        /// </summary>
        /// <param name="datas"></param>
        private void UpDataInfo(QueryDataInfo datas)
        {
            HtmlForm lookupForm = this.OperationContext?.HtmlForm;
            if (datas == null || (datas != null && datas.OfficeDatas == null)) return;
            if (!datas.OfficeDatas.ContainsKey("fstoreid")) return;

            // 获取数据状态和销售部门数据
            var fstatusData = datas.OfficeDatas.ContainsKey("fstatus") ? datas.OfficeDatas["fstatus"] : null;
            var fdeptidData = datas.OfficeDatas.ContainsKey("fdeptid") ? datas.OfficeDatas["fdeptid"] : null;
            var fstoreidData = datas.OfficeDatas["fstoreid"];

            // 如果缺少必要字段，尝试从数据库获取
            if (fstatusData == null || fdeptidData == null)
            {
                var missingFieldsData = GetMissingFieldsData(datas, fstatusData == null, fdeptidData == null);
                if (fstatusData == null) fstatusData = missingFieldsData.StatusData;
                if (fdeptidData == null) fdeptidData = missingFieldsData.DeptIdData;
            }

            // 如果仍然缺少关键字段，记录日志并返回
            if (fdeptidData == null || fstoreidData == null)
            {
                return;
            }

            // 确保数据行数一致
            var rowCount = fstoreidData.Count;
            if (fstatusData != null && fstatusData.Count != rowCount)
            {
                fstatusData = null; // 如果行数不一致，忽略状态字段
            }
            if (fdeptidData.Count != rowCount)
            {
                return;
            }

            // 收集所有需要查询的部门ID
            var deptIdsToQuery = new HashSet<string>();
            for (int i = 0; i < rowCount; i++)
            {
                var status = fstatusData != null ? Convert.ToString(fstatusData[i]) : "";
                var deptId = Convert.ToString(fdeptidData[i]);

                // 当数据状态不为已提交(D)或已审核(E)时，收集部门ID
                // 如果没有状态字段，则处理所有记录
                if ((fstatusData == null || (!status.Equals("D") && !status.Equals("E"))) &&
                    !deptId.IsNullOrEmptyOrWhiteSpace())
                {
                    deptIdsToQuery.Add(deptId);
                }
            }

            // 如果没有需要查询的部门ID，直接返回
            if (!deptIdsToQuery.Any()) return;

            // 一次性查询所有部门对应的门店编码
            var deptStoreMapping = GetStoreCodesByDeptIds(deptIdsToQuery.ToList());

            // 更新数据
            for (int i = 0; i < rowCount; i++)
            {
                var status = fstatusData != null ? Convert.ToString(fstatusData[i]) : "";
                var deptId = Convert.ToString(fdeptidData[i]);

                // 当数据状态不为已提交(D)或已审核(E)时，更新fstoreid
                // 如果没有状态字段，则处理所有记录
                if ((fstatusData == null || (!status.Equals("D") && !status.Equals("E"))) &&
                    !deptId.IsNullOrEmptyOrWhiteSpace())
                {
                    if (deptStoreMapping.ContainsKey(deptId) && !deptStoreMapping[deptId].IsNullOrEmptyOrWhiteSpace())
                    {
                        fstoreidData[i] = deptStoreMapping[deptId];
                    }
                }
            }
        }

        /// <summary>
        /// 获取缺失字段的数据
        /// </summary>
        /// <param name="datas">查询数据信息</param>
        /// <param name="needStatus">是否需要获取状态字段</param>
        /// <param name="needDeptId">是否需要获取部门ID字段</param>
        /// <returns>缺失字段的数据</returns>
        private (List<object> StatusData, List<object> DeptIdData) GetMissingFieldsData(QueryDataInfo datas, bool needStatus, bool needDeptId)
        {
            List<object> statusData = null;
            List<object> deptIdData = null;

            if (!needStatus && !needDeptId) return (statusData, deptIdData);
            
            // 获取主键字段数据，用于查询缺失字段
                var primaryKeyData = GetPrimaryKeyData(datas);
                if (primaryKeyData == null || !primaryKeyData.Any()) return (statusData, deptIdData);

                // 构建查询SQL
                var selectFields = new List<string>();
                if (needStatus) selectFields.Add("fstatus");
                if (needDeptId) selectFields.Add("fdeptid");

                var inClause = string.Join(",", primaryKeyData.Select((id, index) => $"@id{index}"));
                var sql = $@"SELECT fid, {string.Join(", ", selectFields)}
                            FROM t_ydj_order with(nolock)
                            WHERE fid IN ({inClause}) AND fmainorgid = @fmainorgid
                            ORDER BY fid";

                var sqlParams = new List<SqlParam>
                {
                    new SqlParam("@fmainorgid", DbType.String, this.Context.Company)
                };

                // 添加主键参数
                for (int i = 0; i < primaryKeyData.Count; i++)
                {
                    sqlParams.Add(new SqlParam($"@id{i}", DbType.String, primaryKeyData[i]));
                }

                var queryResult = this.DBService.ExecuteDynamicObject(this.Context, sql, sqlParams);

                // 创建ID到数据的映射
                var dataMapping = new Dictionary<string, dynamic>();
                if (queryResult != null && queryResult.Any())
                {
                    foreach (var row in queryResult)
                    {
                        var id = Convert.ToString(row["fid"]);
                        if (!string.IsNullOrEmpty(id))
                        {
                            dataMapping[id] = row;
                        }
                    }
                }

                // 严格按照原始数据顺序构建结果，确保顺序一致性
                if (needStatus)
                {
                    statusData = new List<object>();
                    for (int i = 0; i < primaryKeyData.Count; i++)
                    {
                        var id = primaryKeyData[i];
                        if (dataMapping.ContainsKey(id))
                        {
                            statusData.Add(dataMapping[id]["fstatus"] ?? "");
                        }
                        else
                        {
                            // 如果数据库中没有找到对应记录，使用默认值
                            statusData.Add("");
                        }
                    }
                }

                if (needDeptId)
                {
                    deptIdData = new List<object>();
                    for (int i = 0; i < primaryKeyData.Count; i++)
                    {
                        var id = primaryKeyData[i];
                        if (dataMapping.ContainsKey(id))
                        {
                            deptIdData.Add(dataMapping[id]["fdeptid"] ?? "");
                        }
                        else
                        {
                            // 如果数据库中没有找到对应记录，使用默认值
                            deptIdData.Add("");
                        }
                    }
                }

            try
            {
                

                // 验证数据长度一致性
                /*var expectedCount = primaryKeyData.Count;
                if (statusData != null && statusData.Count != expectedCount)
                {
                    
                }
                if (deptIdData != null && deptIdData.Count != expectedCount)
                {
                    
                }*/
                
            }

            return (statusData, deptIdData);
        }

        /// <summary>
        /// 获取主键字段数据
        /// </summary>
        /// <param name="datas">查询数据信息</param>
        /// <returns>主键数据列表</returns>
        private List<string> GetPrimaryKeyData(QueryDataInfo datas)
        {
            // 尝试常见的主键字段名
            var possibleKeyFields = new[] { "fid", "id", "FID", "ID" };

            foreach (var keyField in possibleKeyFields)
            {
                if (datas.OfficeDatas.ContainsKey(keyField))
                {
                    var primaryKeys = datas.OfficeDatas[keyField].Select(x => Convert.ToString(x)).ToList();
                    return primaryKeys;
                }
            }

            // 如果没有找到标准主键字段，记录可用字段
            var availableFields = string.Join(", ", datas.OfficeDatas.Keys);
            return null;
        }

        /// <summary>
        /// 批量根据销售部门ID获取关联门店编码
        /// </summary>
        /// <param name="deptIds">销售部门ID列表</param>
        /// <returns>部门ID与门店编码的映射字典</returns>
        private Dictionary<string, string> GetStoreCodesByDeptIds(List<string> deptIds)
        {
            var result = new Dictionary<string, string>();

            if (deptIds == null || !deptIds.Any()) return result;

            // 构建IN条件的参数
            var inClause = string.Join(",", deptIds.Select((id, index) => $"@deptId{index}"));

            var sql = $@"SELECT dept.fid as deptid, store.fnumber
                            FROM t_bd_department dept with(nolock)
                            INNER JOIN t_bas_store store with(nolock) ON store.fid = dept.fstore
                            WHERE dept.fid IN ({inClause}) 
                            ORDER BY dept.fid, store.fnumber";

            var sqlParams = new List<SqlParam>();

            // 添加部门ID参数
            for (int i = 0; i < deptIds.Count; i++)
            {
                sqlParams.Add(new SqlParam($"@deptId{i}", DbType.String, deptIds[i]));
            }

            var queryResult = this.DBService.ExecuteDynamicObject(this.Context, sql, sqlParams);

            if (queryResult != null && queryResult.Any())
            {
                // 按部门ID分组，合并同一部门的多个门店编码
                var groupedResult = queryResult.GroupBy(r => Convert.ToString(r["deptid"]))
                    .ToDictionary(
                        g => g.Key,
                        g => string.Join(",", g.Select(r => Convert.ToString(r["fnumber"]))
                            .Where(code => !code.IsNullOrEmptyOrWhiteSpace())
                            .Distinct()
                            .OrderBy(code => code))
                    );

                return groupedResult;
            }

            return result;
        }
    }
}